import { z } from 'zod';

// File Node Schema for project structure
export const FileNodeSchema = z.object({
  id: z.string(),
  name: z.string(),
  path: z.string(),
  type: z.enum(['file', 'directory']),
  size: z.number().optional(),
  content: z.string().optional(),
  language: z.string().optional(),
  lastModified: z.date(),
  children: z.array(z.lazy(() => FileNodeSchema)).optional()
});

export type FileNode = z.infer<typeof FileNodeSchema>;

// Project Schema
export const ProjectSchema = z.object({
  id: z.string(),
  userId: z.string(),
  sessionId: z.string().optional(),
  name: z.string(),
  description: z.string().optional(),
  files: z.record(z.any()).default({}),
  framework: z.string().optional(),
  language: z.string().optional(),
  templateId: z.string().optional(),
  settings: z.record(z.any()).default({}),
  createdAt: z.date(),
  updatedAt: z.date()
});

export type Project = z.infer<typeof ProjectSchema>;

// Project Creation Request
export const CreateProjectRequestSchema = z.object({
  name: z.string().min(1, 'Project name is required'),
  description: z.string().optional(),
  framework: z.string().optional(),
  language: z.string().optional(),
  templateId: z.string().optional(),
  settings: z.record(z.any()).optional()
});

export type CreateProjectRequest = z.infer<typeof CreateProjectRequestSchema>;

// Project Update Request
export const UpdateProjectRequestSchema = z.object({
  name: z.string().optional(),
  description: z.string().optional(),
  framework: z.string().optional(),
  language: z.string().optional(),
  settings: z.record(z.any()).optional()
});

export type UpdateProjectRequest = z.infer<typeof UpdateProjectRequestSchema>;

// File Operation Schemas
export const FileOperationSchema = z.discriminatedUnion('type', [
  z.object({
    type: z.literal('create'),
    path: z.string(),
    content: z.string(),
    language: z.string().optional()
  }),
  z.object({
    type: z.literal('update'),
    path: z.string(),
    content: z.string()
  }),
  z.object({
    type: z.literal('delete'),
    path: z.string()
  }),
  z.object({
    type: z.literal('rename'),
    oldPath: z.string(),
    newPath: z.string()
  }),
  z.object({
    type: z.literal('move'),
    oldPath: z.string(),
    newPath: z.string()
  })
]);

export type FileOperation = z.infer<typeof FileOperationSchema>;

// Project Template Schema
export const ProjectTemplateSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string(),
  framework: z.string(),
  language: z.string(),
  files: z.array(z.object({
    path: z.string(),
    content: z.string(),
    template: z.boolean().default(false)
  })),
  dependencies: z.array(z.string()).optional(),
  scripts: z.record(z.string()).optional(),
  settings: z.record(z.any()).default({}),
  tags: z.array(z.string()).default([]),
  isPublic: z.boolean().default(true)
});

export type ProjectTemplate = z.infer<typeof ProjectTemplateSchema>;

// Export Request Schema
export const ExportRequestSchema = z.object({
  projectId: z.string(),
  format: z.enum(['zip', 'tar', 'git']),
  includeNodeModules: z.boolean().default(false),
  includeDotFiles: z.boolean().default(true)
});

export type ExportRequest = z.infer<typeof ExportRequestSchema>;
