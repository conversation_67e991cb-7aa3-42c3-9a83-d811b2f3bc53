version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    container_name: waatch-postgres
    environment:
      POSTGRES_USER: waatch
      POSTGRES_PASSWORD: waatch_password
      POSTGRES_DB: waatch_db
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U waatch -d waatch_db"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - waatch-network

  redis:
    image: redis:7-alpine
    container_name: waatch-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - waatch-network

  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: waatch-pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "5050:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - waatch-network

  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: waatch-redis-commander
    environment:
      REDIS_HOSTS: local:redis:6379
    ports:
      - "8081:8081"
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - waatch-network

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  pgadmin_data:
    driver: local

networks:
  waatch-network:
    driver: bridge
