import { 
  LoginRequest, 
  RegisterRequest, 
  AuthResponse, 
  User 
} from '@waatch/shared';
import { apiClient } from './apiClient';

class AuthService {
  async login(credentials: LoginRequest): Promise<AuthResponse> {
    const response = await apiClient.post<AuthResponse>('/auth/login', credentials);
    return response.data;
  }

  async register(userData: RegisterRequest): Promise<AuthResponse> {
    const response = await apiClient.post<AuthResponse>('/auth/register', userData);
    return response.data;
  }

  async refreshToken(refreshToken: string): Promise<Omit<AuthResponse, 'user'>> {
    const response = await apiClient.post<Omit<AuthResponse, 'user'>>('/auth/refresh', {
      refreshToken,
    });
    return response.data;
  }

  async logout(refreshToken: string): Promise<void> {
    await apiClient.post('/auth/logout', { refreshToken });
  }

  async getCurrentUser(): Promise<User> {
    const response = await apiClient.get<{ user: User }>('/auth/me');
    return response.data.user;
  }
}

export const authService = new AuthService();
