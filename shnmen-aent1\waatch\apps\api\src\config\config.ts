import { z } from 'zod';

// Environment variables schema
const envSchema = z.object({
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),
  PORT: z.string().transform(Number).default('3001'),
  
  // Database
  DATABASE_URL: z.string(),
  
  // Redis
  REDIS_URL: z.string().default('redis://localhost:6379'),
  
  // JWT
  JWT_SECRET: z.string(),
  JWT_REFRESH_SECRET: z.string(),
  JWT_EXPIRES_IN: z.string().default('15m'),
  JWT_REFRESH_EXPIRES_IN: z.string().default('7d'),
  
  // AI Services
  CHUTES_AI_API_KEY: z.string(),
  CHUTES_AI_BASE_URL: z.string().default('https://api.chutes.ai/v1'),
  CHUTES_AI_MODEL: z.string().default('deepseek-ai/DeepSeek-R1'),
  
  // OpenAI (fallback)
  OPENAI_API_KEY: z.string().optional(),
  OPENAI_MODEL: z.string().default('gpt-4'),
  
  // CORS
  FRONTEND_URL: z.string().default('http://localhost:3000'),
  CORS_ORIGIN: z.string().default('http://localhost:3000'),
  
  // Rate Limiting
  RATE_LIMIT_WINDOW_MS: z.string().transform(Number).default('900000'),
  RATE_LIMIT_MAX_REQUESTS: z.string().transform(Number).default('100'),
  
  // File Upload
  MAX_FILE_SIZE: z.string().transform(Number).default('10485760'),
  UPLOAD_DIR: z.string().default('./uploads'),
  
  // Security
  BCRYPT_ROUNDS: z.string().transform(Number).default('12'),
  SESSION_SECRET: z.string(),
  
  // Features
  ENABLE_SWAGGER: z.string().transform(val => val === 'true').default('true'),
  ENABLE_CORS: z.string().transform(val => val === 'true').default('true'),
  ENABLE_MORGAN_LOGGING: z.string().transform(val => val === 'true').default('true'),
  
  // Logging
  LOG_LEVEL: z.enum(['error', 'warn', 'info', 'debug']).default('info'),
  LOG_FORMAT: z.enum(['simple', 'json', 'combined']).default('combined')
});

// Parse and validate environment variables
const env = envSchema.parse(process.env);

// Configuration object
export const config = {
  env: env.NODE_ENV,
  port: env.PORT,
  
  database: {
    url: env.DATABASE_URL
  },
  
  redis: {
    url: env.REDIS_URL
  },
  
  jwt: {
    secret: env.JWT_SECRET,
    refreshSecret: env.JWT_REFRESH_SECRET,
    expiresIn: env.JWT_EXPIRES_IN,
    refreshExpiresIn: env.JWT_REFRESH_EXPIRES_IN
  },
  
  ai: {
    chutes: {
      apiKey: env.CHUTES_AI_API_KEY,
      baseUrl: env.CHUTES_AI_BASE_URL,
      model: env.CHUTES_AI_MODEL
    },
    openai: {
      apiKey: env.OPENAI_API_KEY,
      model: env.OPENAI_MODEL
    }
  },
  
  cors: {
    origin: env.CORS_ORIGIN,
    enabled: env.ENABLE_CORS
  },
  
  rateLimit: {
    windowMs: env.RATE_LIMIT_WINDOW_MS,
    maxRequests: env.RATE_LIMIT_MAX_REQUESTS
  },
  
  upload: {
    maxFileSize: env.MAX_FILE_SIZE,
    uploadDir: env.UPLOAD_DIR
  },
  
  security: {
    bcryptRounds: env.BCRYPT_ROUNDS,
    sessionSecret: env.SESSION_SECRET
  },
  
  features: {
    swagger: {
      enabled: env.ENABLE_SWAGGER
    },
    morganLogging: env.ENABLE_MORGAN_LOGGING
  },
  
  logging: {
    level: env.LOG_LEVEL,
    format: env.LOG_FORMAT
  },

  // Add swagger config
  swagger: {
    enabled: env.ENABLE_SWAGGER
  }
} as const;
