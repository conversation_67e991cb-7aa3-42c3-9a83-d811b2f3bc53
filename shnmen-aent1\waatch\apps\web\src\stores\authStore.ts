import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';
import { persist } from 'zustand/middleware';
import { User, LoginRequest, RegisterRequest } from '@waatch/shared';
import { authService } from '@/services/authService';
import toast from 'react-hot-toast';

interface AuthState {
  user: User | null;
  accessToken: string | null;
  refreshToken: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

interface AuthActions {
  login: (credentials: LoginRequest) => Promise<void>;
  register: (userData: RegisterRequest) => Promise<void>;
  logout: () => Promise<void>;
  refreshAuth: () => Promise<void>;
  initializeAuth: () => Promise<void>;
  clearError: () => void;
  setLoading: (loading: boolean) => void;
}

type AuthStore = AuthState & AuthActions;

export const useAuthStore = create<AuthStore>()(
  persist(
    immer((set, get) => ({
      // Initial state
      user: null,
      accessToken: null,
      refreshToken: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      // Actions
      login: async (credentials: LoginRequest) => {
        set((state) => {
          state.isLoading = true;
          state.error = null;
        });

        try {
          const response = await authService.login(credentials);
          
          set((state) => {
            state.user = response.user;
            state.accessToken = response.accessToken;
            state.refreshToken = response.refreshToken;
            state.isAuthenticated = true;
            state.isLoading = false;
            state.error = null;
          });

          toast.success('Welcome back!');
        } catch (error: any) {
          set((state) => {
            state.error = error.message || 'Login failed';
            state.isLoading = false;
          });
          
          toast.error(error.message || 'Login failed');
          throw error;
        }
      },

      register: async (userData: RegisterRequest) => {
        set((state) => {
          state.isLoading = true;
          state.error = null;
        });

        try {
          const response = await authService.register(userData);
          
          set((state) => {
            state.user = response.user;
            state.accessToken = response.accessToken;
            state.refreshToken = response.refreshToken;
            state.isAuthenticated = true;
            state.isLoading = false;
            state.error = null;
          });

          toast.success('Account created successfully!');
        } catch (error: any) {
          set((state) => {
            state.error = error.message || 'Registration failed';
            state.isLoading = false;
          });
          
          toast.error(error.message || 'Registration failed');
          throw error;
        }
      },

      logout: async () => {
        const { refreshToken } = get();
        
        try {
          if (refreshToken) {
            await authService.logout(refreshToken);
          }
        } catch (error) {
          console.error('Logout error:', error);
        } finally {
          set((state) => {
            state.user = null;
            state.accessToken = null;
            state.refreshToken = null;
            state.isAuthenticated = false;
            state.error = null;
          });
          
          toast.success('Logged out successfully');
        }
      },

      refreshAuth: async () => {
        const { refreshToken } = get();
        
        if (!refreshToken) {
          throw new Error('No refresh token available');
        }

        try {
          const response = await authService.refreshToken(refreshToken);
          
          set((state) => {
            state.accessToken = response.accessToken;
            state.refreshToken = response.refreshToken;
          });
        } catch (error) {
          // If refresh fails, logout user
          get().logout();
          throw error;
        }
      },

      initializeAuth: async () => {
        const { accessToken, refreshToken } = get();
        
        if (!accessToken || !refreshToken) {
          set((state) => {
            state.isLoading = false;
          });
          return;
        }

        set((state) => {
          state.isLoading = true;
        });

        try {
          // Try to get current user to validate token
          const user = await authService.getCurrentUser();
          
          set((state) => {
            state.user = user;
            state.isAuthenticated = true;
            state.isLoading = false;
          });
        } catch (error) {
          // If getting user fails, try to refresh token
          try {
            await get().refreshAuth();
            const user = await authService.getCurrentUser();
            
            set((state) => {
              state.user = user;
              state.isAuthenticated = true;
              state.isLoading = false;
            });
          } catch (refreshError) {
            // If refresh also fails, logout
            get().logout();
            set((state) => {
              state.isLoading = false;
            });
          }
        }
      },

      clearError: () => {
        set((state) => {
          state.error = null;
        });
      },

      setLoading: (loading: boolean) => {
        set((state) => {
          state.isLoading = loading;
        });
      },
    })),
    {
      name: 'waatch-auth',
      partialize: (state) => ({
        accessToken: state.accessToken,
        refreshToken: state.refreshToken,
        user: state.user,
      }),
    }
  )
);
