import { Router } from 'express';
import { 
  LoginRequestSchema, 
  RegisterRequestSchema, 
  RefreshTokenRequestSchema 
} from '@waatch/shared';
import { asyncHandler } from '../middleware/errorHandler';
import { authRateLimiter } from '../middleware/rateLimiter';
import { authenticateToken } from '../middleware/auth';
import { AuthService } from '../services/auth';
import { logger } from '../utils/logger';

const router = Router();
const authService = new AuthService();

/**
 * @swagger
 * /api/auth/register:
 *   post:
 *     summary: Register a new user
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - password
 *               - firstName
 *               - lastName
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *               password:
 *                 type: string
 *                 minLength: 8
 *               firstName:
 *                 type: string
 *               lastName:
 *                 type: string
 *     responses:
 *       201:
 *         description: User registered successfully
 *       400:
 *         description: Validation error
 *       409:
 *         description: Email already exists
 */
router.post('/register', authRateLimiter, asyncHandler(async (req, res) => {
  const validatedData = RegisterRequestSchema.parse(req.body);
  
  const result = await authService.register(validatedData);
  
  logger.info('User registered', {
    userId: result.user.id,
    email: result.user.email,
    ip: req.ip,
  });
  
  res.status(201).json(result);
}));

/**
 * @swagger
 * /api/auth/login:
 *   post:
 *     summary: Login user
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - password
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *               password:
 *                 type: string
 *     responses:
 *       200:
 *         description: Login successful
 *       400:
 *         description: Validation error
 *       401:
 *         description: Invalid credentials
 */
router.post('/login', authRateLimiter, asyncHandler(async (req, res) => {
  const validatedData = LoginRequestSchema.parse(req.body);
  
  const result = await authService.login(validatedData);
  
  logger.info('User logged in', {
    userId: result.user.id,
    email: result.user.email,
    ip: req.ip,
  });
  
  res.json(result);
}));

/**
 * @swagger
 * /api/auth/refresh:
 *   post:
 *     summary: Refresh access token
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - refreshToken
 *             properties:
 *               refreshToken:
 *                 type: string
 *     responses:
 *       200:
 *         description: Token refreshed successfully
 *       401:
 *         description: Invalid refresh token
 */
router.post('/refresh', asyncHandler(async (req, res) => {
  const validatedData = RefreshTokenRequestSchema.parse(req.body);
  
  const result = await authService.refreshToken(validatedData.refreshToken);
  
  res.json(result);
}));

/**
 * @swagger
 * /api/auth/logout:
 *   post:
 *     summary: Logout user
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Logout successful
 *       401:
 *         description: Unauthorized
 */
router.post('/logout', authenticateToken, asyncHandler(async (req, res) => {
  const refreshToken = req.body.refreshToken;
  
  if (refreshToken) {
    await authService.logout(refreshToken);
  }
  
  logger.info('User logged out', {
    userId: req.user!.id,
    email: req.user!.email,
    ip: req.ip,
  });
  
  res.json({ message: 'Logout successful' });
}));

/**
 * @swagger
 * /api/auth/me:
 *   get:
 *     summary: Get current user profile
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: User profile retrieved successfully
 *       401:
 *         description: Unauthorized
 */
router.get('/me', authenticateToken, asyncHandler(async (req, res) => {
  const user = await authService.getCurrentUser(req.user!.id);
  res.json({ user });
}));

export { router as authRoutes };
