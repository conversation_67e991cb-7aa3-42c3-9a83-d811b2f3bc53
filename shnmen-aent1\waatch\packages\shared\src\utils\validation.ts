import { z } from 'zod';

// Generic validation utility
export function validateSchema<T>(schema: z.ZodSchema<T>, data: unknown): {
  success: boolean;
  data?: T;
  errors?: string[];
} {
  try {
    const result = schema.parse(data);
    return { success: true, data: result };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        success: false,
        errors: error.errors.map(err => `${err.path.join('.')}: ${err.message}`)
      };
    }
    return {
      success: false,
      errors: ['Unknown validation error']
    };
  }
}

// Safe parse utility with better error handling
export function safeParseSchema<T>(schema: z.ZodSchema<T>, data: unknown): {
  success: boolean;
  data?: T;
  error?: {
    message: string;
    issues: Array<{
      path: string;
      message: string;
      code: string;
    }>;
  };
} {
  const result = schema.safeParse(data);
  
  if (result.success) {
    return { success: true, data: result.data };
  }
  
  return {
    success: false,
    error: {
      message: 'Validation failed',
      issues: result.error.errors.map(err => ({
        path: err.path.join('.'),
        message: err.message,
        code: err.code
      }))
    }
  };
}

// Email validation utility
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// Password strength validation
export function validatePasswordStrength(password: string): {
  isValid: boolean;
  score: number;
  feedback: string[];
} {
  const feedback: string[] = [];
  let score = 0;

  if (password.length >= 8) {
    score += 1;
  } else {
    feedback.push('Password must be at least 8 characters long');
  }

  if (/[a-z]/.test(password)) {
    score += 1;
  } else {
    feedback.push('Password must contain at least one lowercase letter');
  }

  if (/[A-Z]/.test(password)) {
    score += 1;
  } else {
    feedback.push('Password must contain at least one uppercase letter');
  }

  if (/\d/.test(password)) {
    score += 1;
  } else {
    feedback.push('Password must contain at least one number');
  }

  if (/[@$!%*?&]/.test(password)) {
    score += 1;
  } else {
    feedback.push('Password must contain at least one special character (@$!%*?&)');
  }

  return {
    isValid: score >= 4,
    score,
    feedback
  };
}

// File path validation
export function isValidFilePath(path: string): boolean {
  // Basic file path validation - can be enhanced based on requirements
  const invalidChars = /[<>:"|?*]/;
  return !invalidChars.test(path) && path.length > 0 && path.length <= 260;
}

// Agent ID validation
export function isValidAgentId(agentId: string): boolean {
  const validAgentIds = [
    'agent.md', 'agent2.md', 'agent3.md', 'agent4.md',
    'agent5.md', 'agent6.md', 'agent7.md', 'agent8.md',
    'agent9.md', 'agent10.md', 'agent11.md', 'agent12.md'
  ];
  return validAgentIds.includes(agentId);
}
