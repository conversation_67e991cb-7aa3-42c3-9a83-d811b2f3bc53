{"name": "waatch", "version": "1.0.0", "description": "Waatch - AI-Powered Multi-Agent Code Generation Platform", "private": true, "workspaces": ["apps/*", "packages/*"], "packageManager": "pnpm@8.15.0", "engines": {"node": ">=20.10.0", "pnpm": ">=8.0.0"}, "scripts": {"build": "turbo build", "dev": "turbo dev", "lint": "turbo lint", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "type-check": "turbo type-check", "test": "turbo test", "test:e2e": "turbo test:e2e", "clean": "turbo clean", "db:generate": "turbo db:generate", "db:push": "turbo db:push", "db:migrate": "turbo db:migrate", "db:studio": "turbo db:studio", "docker:dev": "docker-compose -f docker/docker-compose.dev.yml up -d", "docker:down": "docker-compose -f docker/docker-compose.dev.yml down", "prepare": "husky install"}, "devDependencies": {"@commitlint/cli": "^18.4.3", "@commitlint/config-conventional": "^18.4.3", "@turbo/gen": "^1.11.2", "husky": "^8.0.3", "lint-staged": "^15.2.0", "prettier": "^3.1.1", "turbo": "^1.11.2", "typescript": "^5.3.3"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,md,yml,yaml}": ["prettier --write"]}, "commitlint": {"extends": ["@commitlint/config-conventional"]}}