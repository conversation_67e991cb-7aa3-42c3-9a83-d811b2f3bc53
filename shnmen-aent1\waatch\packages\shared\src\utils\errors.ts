// Custom Error Classes
export class WaatchError extends Error {
  public readonly code: string;
  public readonly statusCode: number;
  public readonly details?: Record<string, any>;

  constructor(
    message: string,
    code: string,
    statusCode: number = 500,
    details?: Record<string, any>
  ) {
    super(message);
    this.name = 'WaatchError';
    this.code = code;
    this.statusCode = statusCode;
    this.details = details;
  }
}

export class ValidationError extends WaatchError {
  constructor(message: string, details?: Record<string, any>) {
    super(message, 'VALIDATION_ERROR', 400, details);
    this.name = 'ValidationError';
  }
}

export class AuthenticationError extends WaatchError {
  constructor(message: string = 'Authentication failed') {
    super(message, 'AUTHENTICATION_ERROR', 401);
    this.name = 'AuthenticationError';
  }
}

export class AuthorizationError extends WaatchError {
  constructor(message: string = 'Insufficient permissions') {
    super(message, 'AUTHORIZATION_ERROR', 403);
    this.name = 'AuthorizationError';
  }
}

export class NotFoundError extends WaatchError {
  constructor(resource: string) {
    super(`${resource} not found`, 'NOT_FOUND_ERROR', 404);
    this.name = 'NotFoundError';
  }
}

export class ConflictError extends WaatchError {
  constructor(message: string) {
    super(message, 'CONFLICT_ERROR', 409);
    this.name = 'ConflictError';
  }
}

export class RateLimitError extends WaatchError {
  constructor(message: string = 'Rate limit exceeded') {
    super(message, 'RATE_LIMIT_ERROR', 429);
    this.name = 'RateLimitError';
  }
}

export class AgentError extends WaatchError {
  constructor(message: string, agentId?: string) {
    super(message, 'AGENT_ERROR', 500, { agentId });
    this.name = 'AgentError';
  }
}

export class ToolError extends WaatchError {
  constructor(message: string, toolName?: string) {
    super(message, 'TOOL_ERROR', 500, { toolName });
    this.name = 'ToolError';
  }
}

export class DatabaseError extends WaatchError {
  constructor(message: string, operation?: string) {
    super(message, 'DATABASE_ERROR', 500, { operation });
    this.name = 'DatabaseError';
  }
}

export class ExternalServiceError extends WaatchError {
  constructor(message: string, service?: string) {
    super(message, 'EXTERNAL_SERVICE_ERROR', 502, { service });
    this.name = 'ExternalServiceError';
  }
}

// Error Response Interface
export interface ErrorResponse {
  error: {
    message: string;
    code: string;
    details?: Record<string, any>;
    timestamp: string;
    requestId?: string;
  };
}

// Error Utility Functions
export function createErrorResponse(
  error: WaatchError | Error,
  requestId?: string
): ErrorResponse {
  if (error instanceof WaatchError) {
    return {
      error: {
        message: error.message,
        code: error.code,
        details: error.details,
        timestamp: new Date().toISOString(),
        requestId
      }
    };
  }

  return {
    error: {
      message: error.message || 'Internal server error',
      code: 'INTERNAL_ERROR',
      timestamp: new Date().toISOString(),
      requestId
    }
  };
}

export function isWaatchError(error: any): error is WaatchError {
  return error instanceof WaatchError;
}
