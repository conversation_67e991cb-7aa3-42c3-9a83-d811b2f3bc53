import winston from 'winston';
import { config } from '../config/config';

// Define log levels
const levels = {
  error: 0,
  warn: 1,
  info: 2,
  debug: 3,
};

// Define colors for each level
const colors = {
  error: 'red',
  warn: 'yellow',
  info: 'green',
  debug: 'blue',
};

// Add colors to winston
winston.addColors(colors);

// Create custom format
const customFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.prettyPrint()
);

// Development format with colors
const developmentFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.errors({ stack: true }),
  winston.format.colorize({ all: true }),
  winston.format.printf(({ timestamp, level, message, ...meta }) => {
    let log = `${timestamp} [${level}]: ${message}`;
    
    // Add metadata if present
    if (Object.keys(meta).length > 0) {
      log += `\n${JSON.stringify(meta, null, 2)}`;
    }
    
    return log;
  })
);

// Create transports
const transports: winston.transport[] = [
  // Console transport
  new winston.transports.Console({
    format: config.env === 'development' ? developmentFormat : customFormat,
  }),
];

// Add file transports in production
if (config.env === 'production') {
  transports.push(
    // Error log file
    new winston.transports.File({
      filename: 'logs/error.log',
      level: 'error',
      format: customFormat,
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    }),
    
    // Combined log file
    new winston.transports.File({
      filename: 'logs/combined.log',
      format: customFormat,
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    })
  );
}

// Create logger instance
export const logger = winston.createLogger({
  level: config.logging.level,
  levels,
  format: customFormat,
  transports,
  exitOnError: false,
});

// Create a stream for Morgan HTTP logging
export const morganStream = {
  write: (message: string) => {
    logger.info(message.trim());
  },
};

// Helper functions for structured logging
export const loggers = {
  // HTTP request logging
  httpRequest: (req: any, res: any, responseTime: number) => {
    logger.info('HTTP Request', {
      method: req.method,
      url: req.url,
      statusCode: res.statusCode,
      responseTime: `${responseTime}ms`,
      userAgent: req.get('User-Agent'),
      ip: req.ip,
      userId: req.user?.id,
    });
  },

  // Database operation logging
  dbOperation: (operation: string, table: string, duration: number, success: boolean) => {
    logger.info('Database Operation', {
      operation,
      table,
      duration: `${duration}ms`,
      success,
    });
  },

  // AI service logging
  aiRequest: (provider: string, model: string, tokensUsed: number, responseTime: number) => {
    logger.info('AI Request', {
      provider,
      model,
      tokensUsed,
      responseTime: `${responseTime}ms`,
    });
  },

  // Authentication logging
  auth: (action: string, userId?: string, success: boolean = true, error?: string) => {
    logger.info('Authentication', {
      action,
      userId,
      success,
      error,
    });
  },

  // Error logging with context
  error: (error: Error, context?: Record<string, any>) => {
    logger.error('Application Error', {
      message: error.message,
      stack: error.stack,
      name: error.name,
      ...context,
    });
  },

  // Performance logging
  performance: (operation: string, duration: number, metadata?: Record<string, any>) => {
    logger.info('Performance Metric', {
      operation,
      duration: `${duration}ms`,
      ...metadata,
    });
  },

  // Security logging
  security: (event: string, severity: 'low' | 'medium' | 'high', details: Record<string, any>) => {
    logger.warn('Security Event', {
      event,
      severity,
      timestamp: new Date().toISOString(),
      ...details,
    });
  },
};

// Export default logger
export default logger;
