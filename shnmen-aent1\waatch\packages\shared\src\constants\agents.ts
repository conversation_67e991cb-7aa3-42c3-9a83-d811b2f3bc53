// Agent IDs and Configuration Constants
export const AGENT_IDS = {
  PAIR_PROGRAMMING: 'agent.md',
  DATABASE_EXPERT: 'agent2.md',
  DEVOPS_EXPERT: 'agent3.md',
  DEVIN_ENGINEER: 'agent4.md',
  LOVABLE_EDITOR: 'agent5.md',
  BOLT_EXPERT: 'agent6.md',
  CLINE_ENGINEER: 'agent7.md',
  ROO_ENGINEER: 'agent8.md',
  REPLIT_EXPERT: 'agent9.md',
  TRAE_AI: 'agent10.md',
  VERCEL_ASSISTANT: 'agent11.md',
  GITHUB_COPILOT: 'agent12.md'
} as const;

export type AgentId = typeof AGENT_IDS[keyof typeof AGENT_IDS];

// Agent Capabilities
export const AGENT_CAPABILITIES = {
  CODE_GENERATION: 'code_generation',
  DATABASE_DESIGN: 'database_design',
  DEVOPS_AUTOMATION: 'devops_automation',
  UI_DEVELOPMENT: 'ui_development',
  TESTING: 'testing',
  DEPLOYMENT: 'deployment',
  DEBUGGING: 'debugging',
  REFACTORING: 'refactoring',
  DOCUMENTATION: 'documentation',
  PERFORMANCE_OPTIMIZATION: 'performance_optimization',
  SECURITY_ANALYSIS: 'security_analysis',
  API_DESIGN: 'api_design'
} as const;

export type AgentCapability = typeof AGENT_CAPABILITIES[keyof typeof AGENT_CAPABILITIES];

// Agent Categories
export const AGENT_CATEGORIES = {
  DEVELOPMENT: 'development',
  INFRASTRUCTURE: 'infrastructure',
  DESIGN: 'design',
  ANALYSIS: 'analysis',
  AUTOMATION: 'automation'
} as const;

export type AgentCategory = typeof AGENT_CATEGORIES[keyof typeof AGENT_CATEGORIES];

// Default Model Configurations
export const DEFAULT_MODEL_CONFIGS = {
  CHUTES_DEEPSEEK: {
    provider: 'chutes' as const,
    model: 'deepseek-ai/DeepSeek-R1',
    temperature: 0.7,
    maxTokens: 4096,
    topP: 0.9
  },
  OPENAI_GPT4: {
    provider: 'openai' as const,
    model: 'gpt-4',
    temperature: 0.7,
    maxTokens: 4096,
    topP: 0.9
  },
  ANTHROPIC_CLAUDE: {
    provider: 'anthropic' as const,
    model: 'claude-3-sonnet-20240229',
    temperature: 0.7,
    maxTokens: 4096,
    topP: 0.9
  }
} as const;

// Tool Permissions
export const TOOL_PERMISSIONS = {
  READ_CODEBASE: 'read:codebase',
  WRITE_FILES: 'write:files',
  EXECUTE_COMMANDS: 'execute:commands',
  ACCESS_DATABASE: 'access:database',
  DEPLOY_SERVICES: 'deploy:services',
  MANAGE_INFRASTRUCTURE: 'manage:infrastructure',
  ACCESS_WEB: 'access:web',
  MANAGE_PACKAGES: 'manage:packages'
} as const;

export type ToolPermission = typeof TOOL_PERMISSIONS[keyof typeof TOOL_PERMISSIONS];

// Agent Complexity Levels
export const COMPLEXITY_LEVELS = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high'
} as const;

export type ComplexityLevel = typeof COMPLEXITY_LEVELS[keyof typeof COMPLEXITY_LEVELS];
