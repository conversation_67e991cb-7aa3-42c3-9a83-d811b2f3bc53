# Waatch - AI-Powered Multi-Agent Code Generation Platform

Waatch is a comprehensive full-stack application that integrates with the Shinmen AI agent ecosystem, providing an interactive multi-agent chat interface for code generation, project management, and AI-assisted development.

## 🚀 Features

### Core Capabilities
- **12 Specialized AI Agents** - Each with unique capabilities and tools
- **Multi-Agent Chat Interface** - Seamless switching between agents with context preservation
- **Real-time Code Generation** - Live streaming responses with syntax highlighting
- **Project Management** - Create, edit, and export complete projects
- **Advanced Authentication** - JWT-based auth with refresh tokens
- **Real-time Collaboration** - WebSocket-powered live updates

### AI Agent Ecosystem
- **Pair Programming Agent** - Context-aware coding assistance
- **DataForge** - Database design and SQL optimization
- **CloudOps** - DevOps and infrastructure automation
- **Devin Engineer** - Autonomous software development
- **Lovable Editor** - React/web development specialist
- **Bolt Expert** - WebContainer and instant deployment
- **Cline Engineer** - Advanced MCP protocol integration
- **Roo Engineer** - Maintainability-focused development
- **Replit Expert** - Platform-specific workflows
- **Trae AI** - Web integration and citations
- **v0 Vercel Assistant** - Next.js and MDX processing
- **GitHub Copilot** - Comprehensive code analysis

## 🏗️ Architecture

### Technology Stack
- **Frontend**: React 18.2+ with TypeScript 5.0+
- **Backend**: Node.js 20+ with Express 4.18+
- **Database**: PostgreSQL 15+ with Prisma ORM 5.7+
- **Cache**: Redis 7.2+ for sessions and caching
- **AI Integration**: Chutes AI with deepseek-ai/DeepSeek-R1 model
- **Build System**: Turborepo with pnpm workspaces
- **Styling**: Tailwind CSS 3.4+ with shadcn/ui components
- **State Management**: Zustand 4.4+ with Immer

### Project Structure
```
waatch/
├── apps/
│   ├── web/                 # React frontend application
│   └── api/                 # Express.js backend application
├── packages/
│   ├── shared/              # Shared TypeScript types and utilities
│   ├── ui/                  # Shared UI component library
│   └── config/              # Shared configuration files
├── docker/                  # Docker configuration
└── docs/                    # Documentation
```

## 🚦 Getting Started

### Prerequisites
- Node.js 20.10+ LTS
- pnpm 8.0+
- Docker & Docker Compose
- PostgreSQL 15+
- Redis 7.2+

### Quick Start

1. **Clone and Install**
   ```bash
   git clone <repository-url>
   cd waatch
   pnpm install
   ```

2. **Environment Setup**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Start Development Environment**
   ```bash
   # Start databases
   pnpm docker:dev
   
   # Run database migrations
   pnpm db:migrate
   
   # Start development servers
   pnpm dev
   ```

4. **Access Applications**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:3001
   - API Documentation: http://localhost:3001/api-docs
   - Database Admin: http://localhost:5050 (pgAdmin)
   - Redis Admin: http://localhost:8081 (Redis Commander)

### Environment Variables

Key environment variables to configure:

```env
# Database
DATABASE_URL="postgresql://waatch:waatch_password@localhost:5432/waatch_db"

# Redis
REDIS_URL="redis://localhost:6379"

# JWT Authentication
JWT_SECRET="your-super-secret-jwt-key"
JWT_REFRESH_SECRET="your-super-secret-refresh-key"

# Chutes AI Integration
CHUTES_AI_API_KEY="fw_3ZnfzTqUwj6AWAcQ8as7VfxG"
CHUTES_AI_BASE_URL="https://api.chutes.ai/v1"
CHUTES_AI_MODEL="deepseek-ai/DeepSeek-R1"

# Application
NODE_ENV="development"
PORT=3001
FRONTEND_URL="http://localhost:3000"
```

## 🛠️ Development

### Available Scripts

```bash
# Development
pnpm dev              # Start all development servers
pnpm build            # Build all applications
pnpm test             # Run all tests
pnpm lint             # Lint all code
pnpm type-check       # TypeScript type checking

# Database
pnpm db:generate      # Generate Prisma client
pnpm db:migrate       # Run database migrations
pnpm db:push          # Push schema changes
pnpm db:studio        # Open Prisma Studio

# Docker
pnpm docker:dev       # Start development containers
pnpm docker:down      # Stop development containers
```

### Code Quality

The project enforces strict code quality standards:

- **TypeScript**: Strict mode with comprehensive type checking
- **ESLint**: Extended rules for React and TypeScript
- **Prettier**: Consistent code formatting
- **Husky**: Pre-commit hooks for quality checks
- **Conventional Commits**: Standardized commit messages

### Testing Strategy

- **Unit Tests**: Vitest with React Testing Library
- **Integration Tests**: API endpoint testing with Supertest
- **E2E Tests**: Playwright for full user workflows
- **Coverage**: Minimum 85% coverage requirement

## 🔧 Configuration

### AI Agent Configuration

Each agent is configured with:
- Unique system prompts and capabilities
- Specialized tool sets and permissions
- Model configuration (temperature, max tokens, etc.)
- Performance monitoring and analytics

### Security Features

- JWT authentication with refresh token rotation
- Rate limiting on all endpoints
- Input validation and sanitization
- CORS configuration
- Helmet.js security headers
- SQL injection prevention
- XSS protection

### Performance Optimizations

- Redis caching for API responses
- Database query optimization with indexes
- Code splitting and lazy loading
- Image optimization and compression
- CDN integration for static assets

## 📚 API Documentation

The API is fully documented with OpenAPI 3.0 specification. Access the interactive documentation at:
- Development: http://localhost:3001/api-docs
- Production: https://api.waatch.dev/api-docs

### Key Endpoints

```
Authentication:
POST /api/auth/register    # User registration
POST /api/auth/login       # User login
POST /api/auth/refresh     # Token refresh
POST /api/auth/logout      # User logout

Agents:
GET  /api/agents           # List all agents
GET  /api/agents/:id       # Get agent details
POST /api/agents/:id/chat  # Chat with agent

Chat:
GET  /api/chat/sessions    # List chat sessions
POST /api/chat/sessions    # Create new session
GET  /api/chat/sessions/:id/messages  # Get messages

Projects:
GET  /api/projects         # List projects
POST /api/projects         # Create project
GET  /api/projects/:id     # Get project details
PUT  /api/projects/:id     # Update project
```

## 🚀 Deployment

### Production Deployment

1. **Build Applications**
   ```bash
   pnpm build
   ```

2. **Database Setup**
   ```bash
   pnpm db:migrate
   ```

3. **Start Production Server**
   ```bash
   pnpm start
   ```

### Docker Deployment

```bash
# Build production images
docker-compose -f docker-compose.prod.yml build

# Start production stack
docker-compose -f docker-compose.prod.yml up -d
```

### Environment-Specific Configurations

- **Development**: Hot reload, debug logging, Swagger UI
- **Staging**: Production-like with debug features
- **Production**: Optimized builds, security hardening, monitoring

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'feat: add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Guidelines

- Follow TypeScript strict mode
- Write comprehensive tests
- Update documentation
- Follow conventional commit format
- Ensure all checks pass

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: [docs.waatch.dev](https://docs.waatch.dev)
- **Issues**: [GitHub Issues](https://github.com/waatch/waatch/issues)
- **Discussions**: [GitHub Discussions](https://github.com/waatch/waatch/discussions)
- **Email**: <EMAIL>

---

Built with ❤️ by the Waatch team
