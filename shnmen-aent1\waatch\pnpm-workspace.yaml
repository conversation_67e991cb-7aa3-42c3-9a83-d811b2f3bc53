packages:
  - "apps/*"
  - "packages/*"

catalog:
  # React & Frontend
  react: ^18.2.0
  react-dom: ^18.2.0
  "@types/react": ^18.2.45
  "@types/react-dom": ^18.2.18
  
  # TypeScript & Build Tools
  typescript: ^5.3.3
  vite: ^5.0.10
  "@vitejs/plugin-react": ^4.2.1
  
  # UI & Styling
  tailwindcss: ^3.4.0
  "@tailwindcss/typography": ^0.5.10
  "class-variance-authority": ^0.7.0
  clsx: ^2.0.0
  "tailwind-merge": ^2.2.0
  "lucide-react": ^0.303.0
  
  # State Management
  zustand: ^4.4.7
  immer: ^10.0.3
  
  # Backend & API
  express: ^4.18.2
  "@types/express": ^4.17.21
  cors: ^2.8.5
  "@types/cors": ^2.8.17
  helmet: ^7.1.0
  
  # Database & ORM
  prisma: ^5.7.1
  "@prisma/client": ^5.7.1
  
  # Authentication & Security
  jsonwebtoken: ^9.0.2
  "@types/jsonwebtoken": ^9.0.5
  bcryptjs: ^2.4.3
  "@types/bcryptjs": ^2.4.6
  
  # Redis & Caching
  redis: ^4.6.12
  "@types/redis": ^4.0.11
  
  # Validation & Utilities
  zod: ^3.22.4
  dotenv: ^16.3.1
  
  # Testing
  vitest: ^1.1.0
  "@testing-library/react": ^14.1.2
  "@testing-library/jest-dom": ^6.1.6
  "@testing-library/user-event": ^14.5.1
  
  # Linting & Formatting
  eslint: ^8.56.0
  "@typescript-eslint/eslint-plugin": ^6.17.0
  "@typescript-eslint/parser": ^6.17.0
  prettier: ^3.1.1
  
  # AI Integration
  openai: ^4.24.1
  
  # WebSocket
  socket.io: ^4.7.4
  socket.io-client: ^4.7.4
  "@types/socket.io": ^3.0.2
