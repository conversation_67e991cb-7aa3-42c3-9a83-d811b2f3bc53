import { z } from 'zod';

// Agent Configuration Schema
export const AgentConfigSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string(),
  systemPrompt: z.string(),
  capabilities: z.array(z.string()),
  tools: z.array(z.object({
    name: z.string(),
    description: z.string(),
    schema: z.record(z.any()),
    permissions: z.array(z.string())
  })),
  modelConfig: z.object({
    provider: z.enum(['chutes', 'openai', 'anthropic']),
    model: z.string(),
    temperature: z.number().min(0).max(2),
    maxTokens: z.number().positive(),
    topP: z.number().min(0).max(1).optional(),
    frequencyPenalty: z.number().min(-2).max(2).optional(),
    presencePenalty: z.number().min(-2).max(2).optional()
  }),
  version: z.string(),
  isActive: z.boolean()
});

export type AgentConfig = z.infer<typeof AgentConfigSchema>;

// Agent Context Schema
export const AgentContextSchema = z.object({
  sessionId: z.string(),
  userId: z.string(),
  workspaceState: z.object({
    id: z.string(),
    openFiles: z.array(z.string()),
    currentFile: z.string().optional(),
    cursorPosition: z.object({
      line: z.number(),
      column: z.number()
    }).optional(),
    editHistory: z.array(z.object({
      type: z.enum(['create', 'update', 'delete', 'rename']),
      path: z.string(),
      content: z.string().optional(),
      timestamp: z.date(),
      agentId: z.string()
    })),
    projectStructure: z.array(z.any()) // FileNode type to be defined
  }),
  permissions: z.array(z.string()),
  agentConfig: AgentConfigSchema
});

export type AgentContext = z.infer<typeof AgentContextSchema>;

// Tool Result Schema
export const ToolResultSchema = z.object({
  success: z.boolean(),
  data: z.any().optional(),
  error: z.string().optional(),
  metadata: z.object({
    executionTime: z.number(),
    resourcesUsed: z.record(z.any())
  }).optional()
});

export type ToolResult = z.infer<typeof ToolResultSchema>;

// Agent Response Schema
export const AgentResponseSchema = z.object({
  id: z.string(),
  agentId: z.string(),
  content: z.string(),
  toolCalls: z.array(z.object({
    tool: z.string(),
    parameters: z.record(z.any()),
    result: ToolResultSchema.optional()
  })).optional(),
  metadata: z.object({
    tokensUsed: z.number().optional(),
    processingTime: z.number().optional(),
    model: z.string().optional()
  }).optional(),
  timestamp: z.date()
});

export type AgentResponse = z.infer<typeof AgentResponseSchema>;

// Agent Selection Criteria
export const AgentSelectionCriteriaSchema = z.object({
  task: z.string(),
  language: z.string().optional(),
  framework: z.string().optional(),
  complexity: z.enum(['low', 'medium', 'high']).optional(),
  requiredCapabilities: z.array(z.string()).optional()
});

export type AgentSelectionCriteria = z.infer<typeof AgentSelectionCriteriaSchema>;
