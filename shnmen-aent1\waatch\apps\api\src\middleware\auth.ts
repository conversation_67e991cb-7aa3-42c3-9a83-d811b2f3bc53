import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { config } from '../config/config';
import { AuthenticationError, AuthorizationError } from '@waatch/shared';
import { prisma } from '../services/database';
import { logger } from '../utils/logger';

// Extend Request interface to include user
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        email: string;
        firstName?: string;
        lastName?: string;
        isActive: boolean;
      };
    }
  }
}

// JWT payload interface
interface JWTPayload {
  userId: string;
  email: string;
  iat: number;
  exp: number;
  type: 'access' | 'refresh';
}

// Authentication middleware
export async function authenticateToken(
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      throw new AuthenticationError('Access token required');
    }

    // Verify JWT token
    const payload = jwt.verify(token, config.jwt.secret) as JWTPayload;

    if (payload.type !== 'access') {
      throw new AuthenticationError('Invalid token type');
    }

    // Get user from database
    const user = await prisma.user.findUnique({
      where: { id: payload.userId },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        isActive: true,
      },
    });

    if (!user) {
      throw new AuthenticationError('User not found');
    }

    if (!user.isActive) {
      throw new AuthenticationError('Account is deactivated');
    }

    // Attach user to request
    req.user = user;

    // Log authentication success
    logger.info('Authentication successful', {
      userId: user.id,
      email: user.email,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    });

    next();
  } catch (error) {
    if (error instanceof jwt.JsonWebTokenError) {
      next(new AuthenticationError('Invalid token'));
    } else if (error instanceof jwt.TokenExpiredError) {
      next(new AuthenticationError('Token expired'));
    } else {
      next(error);
    }
  }
}

// Optional authentication middleware (doesn't throw if no token)
export async function optionalAuth(
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
      return next();
    }

    const payload = jwt.verify(token, config.jwt.secret) as JWTPayload;

    if (payload.type !== 'access') {
      return next();
    }

    const user = await prisma.user.findUnique({
      where: { id: payload.userId },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        isActive: true,
      },
    });

    if (user && user.isActive) {
      req.user = user;
    }

    next();
  } catch (error) {
    // Ignore authentication errors in optional auth
    next();
  }
}

// Authorization middleware factory
export function requirePermissions(...permissions: string[]) {
  return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        throw new AuthenticationError('Authentication required');
      }

      // For now, we'll implement basic role-based authorization
      // In the future, this can be extended with more complex permission systems
      
      // Admin users have all permissions
      const isAdmin = req.user.email.endsWith('@waatch.dev'); // Simple admin check
      
      if (isAdmin) {
        return next();
      }

      // Check specific permissions (to be implemented based on requirements)
      // For now, all authenticated users have basic permissions
      const allowedPermissions = ['read', 'write', 'chat', 'project'];
      const hasPermission = permissions.every(permission => 
        allowedPermissions.some(allowed => permission.startsWith(allowed))
      );

      if (!hasPermission) {
        throw new AuthorizationError('Insufficient permissions');
      }

      next();
    } catch (error) {
      next(error);
    }
  };
}

// Middleware to check if user owns resource
export function requireOwnership(resourceIdParam: string = 'id') {
  return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        throw new AuthenticationError('Authentication required');
      }

      const resourceId = req.params[resourceIdParam];
      if (!resourceId) {
        throw new AuthorizationError('Resource ID required');
      }

      // This is a generic ownership check
      // Specific implementations should override this logic
      req.resourceId = resourceId;
      next();
    } catch (error) {
      next(error);
    }
  };
}

// Extend Request interface for resource ownership
declare global {
  namespace Express {
    interface Request {
      resourceId?: string;
    }
  }
}
