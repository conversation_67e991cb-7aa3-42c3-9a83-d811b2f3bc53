import { z } from 'zod';

// Message Schema
export const MessageSchema = z.object({
  id: z.string(),
  sessionId: z.string(),
  role: z.enum(['user', 'assistant', 'system']),
  content: z.string(),
  metadata: z.record(z.any()).default({}),
  toolCalls: z.array(z.object({
    tool: z.string(),
    parameters: z.record(z.any()),
    result: z.any().optional()
  })).optional(),
  toolResults: z.array(z.any()).optional(),
  tokensUsed: z.number().optional(),
  processingTimeMs: z.number().optional(),
  createdAt: z.date()
});

export type Message = z.infer<typeof MessageSchema>;

// Chat Session Schema
export const ChatSessionSchema = z.object({
  id: z.string(),
  userId: z.string(),
  agentId: z.string(),
  title: z.string().optional(),
  context: z.record(z.any()).default({}),
  workspaceState: z.record(z.any()).default({}),
  createdAt: z.date(),
  updatedAt: z.date(),
  lastActivity: z.date(),
  isArchived: z.boolean().default(false)
});

export type ChatSession = z.infer<typeof ChatSessionSchema>;

// Chat Request Schema
export const ChatRequestSchema = z.object({
  message: z.string().min(1, 'Message cannot be empty'),
  agentId: z.string(),
  sessionId: z.string().optional(),
  context: z.record(z.any()).optional()
});

export type ChatRequest = z.infer<typeof ChatRequestSchema>;

// Chat Response Schema
export const ChatResponseSchema = z.object({
  message: MessageSchema,
  session: ChatSessionSchema,
  suggestions: z.array(z.string()).optional()
});

export type ChatResponse = z.infer<typeof ChatResponseSchema>;

// WebSocket Event Schemas
export const SocketEventSchema = z.discriminatedUnion('type', [
  z.object({
    type: z.literal('message:start'),
    data: z.object({
      sessionId: z.string(),
      messageId: z.string()
    })
  }),
  z.object({
    type: z.literal('message:chunk'),
    data: z.object({
      sessionId: z.string(),
      messageId: z.string(),
      chunk: z.string()
    })
  }),
  z.object({
    type: z.literal('message:end'),
    data: z.object({
      sessionId: z.string(),
      messageId: z.string(),
      message: MessageSchema
    })
  }),
  z.object({
    type: z.literal('agent:switch'),
    data: z.object({
      sessionId: z.string(),
      fromAgent: z.string(),
      toAgent: z.string()
    })
  }),
  z.object({
    type: z.literal('typing:start'),
    data: z.object({
      sessionId: z.string(),
      agentId: z.string()
    })
  }),
  z.object({
    type: z.literal('typing:stop'),
    data: z.object({
      sessionId: z.string(),
      agentId: z.string()
    })
  }),
  z.object({
    type: z.literal('error'),
    data: z.object({
      sessionId: z.string(),
      error: z.string(),
      code: z.string().optional()
    })
  })
]);

export type SocketEvent = z.infer<typeof SocketEventSchema>;

// Agent Switch Request
export const AgentSwitchRequestSchema = z.object({
  sessionId: z.string(),
  newAgentId: z.string(),
  preserveContext: z.boolean().default(true)
});

export type AgentSwitchRequest = z.infer<typeof AgentSwitchRequestSchema>;
