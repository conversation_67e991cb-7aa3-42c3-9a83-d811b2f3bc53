@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer components {
  /* Custom scrollbar */
  .custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--muted-foreground)) transparent;
  }

  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background-color: hsl(var(--muted-foreground));
    border-radius: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background-color: hsl(var(--foreground));
  }

  /* Chat message animations */
  .message-enter {
    animation: slide-in-from-bottom 0.3s ease-out;
  }

  .typing-indicator {
    animation: pulse-glow 1.5s ease-in-out infinite;
  }

  /* Code block styling */
  .code-block {
    @apply relative rounded-lg border bg-muted p-4 font-mono text-sm;
  }

  .code-block pre {
    @apply m-0 overflow-x-auto;
  }

  .code-block code {
    @apply text-foreground;
  }

  /* Agent status indicators */
  .agent-status-online {
    @apply h-2 w-2 rounded-full bg-green-500;
    animation: pulse-glow 2s ease-in-out infinite;
  }

  .agent-status-busy {
    @apply h-2 w-2 rounded-full bg-yellow-500;
  }

  .agent-status-offline {
    @apply h-2 w-2 rounded-full bg-gray-400;
  }

  /* Loading states */
  .skeleton {
    @apply animate-pulse rounded-md bg-muted;
  }

  /* Focus states */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2;
  }

  /* Gradient backgrounds */
  .gradient-primary {
    background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--primary)) 100%);
  }

  .gradient-secondary {
    background: linear-gradient(135deg, hsl(var(--secondary)) 0%, hsl(var(--muted)) 100%);
  }

  /* Glass morphism effect */
  .glass {
    @apply backdrop-blur-sm bg-background/80 border border-border/50;
  }

  /* Monaco Editor theme integration */
  .monaco-editor {
    @apply rounded-lg border;
  }

  .monaco-editor .margin {
    background-color: hsl(var(--muted)) !important;
  }

  .monaco-editor .monaco-editor-background {
    background-color: hsl(var(--background)) !important;
  }

  .monaco-editor .mtk1 {
    color: hsl(var(--foreground)) !important;
  }
}
