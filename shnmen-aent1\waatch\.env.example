# Database Configuration
DATABASE_URL="postgresql://waatch:waatch_password@localhost:5432/waatch_db"
POSTGRES_USER=waatch
POSTGRES_PASSWORD=waatch_password
POSTGRES_DB=waatch_db

# Redis Configuration
REDIS_URL="redis://localhost:6379"
REDIS_PASSWORD=""

# JWT Configuration
JWT_SECRET="your-super-secret-jwt-key-change-in-production"
JWT_REFRESH_SECRET="your-super-secret-refresh-key-change-in-production"
JWT_EXPIRES_IN="15m"
JWT_REFRESH_EXPIRES_IN="7d"

# Chutes AI Configuration
CHUTES_AI_API_KEY="fw_3ZnfzTqUwj6AWAcQ8as7VfxG"
CHUTES_AI_BASE_URL="https://api.chutes.ai/v1"
CHUTES_AI_MODEL="deepseek-ai/DeepSeek-R1"

# OpenAI Fallback Configuration (Optional)
OPENAI_API_KEY=""
OPENAI_MODEL="gpt-4"

# Application Configuration
NODE_ENV="development"
PORT=3001
FRONTEND_URL="http://localhost:3000"
CORS_ORIGIN="http://localhost:3000"

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# File Upload Configuration
MAX_FILE_SIZE=10485760
UPLOAD_DIR="./uploads"

# Logging Configuration
LOG_LEVEL="info"
LOG_FORMAT="combined"

# Security Configuration
BCRYPT_ROUNDS=12
SESSION_SECRET="your-session-secret-change-in-production"

# Development Configuration
ENABLE_SWAGGER=true
ENABLE_CORS=true
ENABLE_MORGAN_LOGGING=true

# Production Configuration (Override in production)
# SSL_CERT_PATH=""
# SSL_KEY_PATH=""
# ENABLE_HTTPS=false
