{"name": "@waatch/web", "version": "1.0.0", "description": "Waatch Frontend - React application with AI agent chat interface", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "clean": "rm -rf dist", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts,.tsx", "test": "vitest", "test:watch": "vitest --watch", "test:coverage": "vitest --coverage"}, "dependencies": {"@waatch/shared": "workspace:*", "react": "catalog:", "react-dom": "catalog:", "zustand": "catalog:", "immer": "catalog:", "clsx": "catalog:", "tailwind-merge": "catalog:", "class-variance-authority": "catalog:", "lucide-react": "catalog:", "socket.io-client": "catalog:", "@monaco-editor/react": "^4.6.0", "react-router-dom": "^6.20.1", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "react-hot-toast": "^2.4.1", "framer-motion": "^10.16.16", "react-markdown": "^9.0.1", "remark-gfm": "^4.0.0", "rehype-highlight": "^7.0.0", "date-fns": "^3.0.6"}, "devDependencies": {"@waatch/config": "workspace:*", "@types/react": "catalog:", "@types/react-dom": "catalog:", "typescript": "catalog:", "vite": "catalog:", "@vitejs/plugin-react": "catalog:", "tailwindcss": "catalog:", "@tailwindcss/typography": "catalog:", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "eslint": "catalog:", "@typescript-eslint/eslint-plugin": "catalog:", "@typescript-eslint/parser": "catalog:", "vitest": "catalog:", "@testing-library/react": "catalog:", "@testing-library/jest-dom": "catalog:", "@testing-library/user-event": "catalog:", "jsdom": "^23.0.1"}, "engines": {"node": ">=20.10.0"}}