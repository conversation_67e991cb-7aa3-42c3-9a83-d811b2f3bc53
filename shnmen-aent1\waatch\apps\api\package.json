{"name": "@waatch/api", "version": "1.0.0", "description": "Waatch API Server - Express.js backend with AI agent integration", "main": "dist/index.js", "scripts": {"build": "tsc", "dev": "tsx watch src/index.ts", "start": "node dist/index.js", "clean": "rm -rf dist", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts", "test": "vitest", "test:watch": "vitest --watch", "test:coverage": "vitest --coverage", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "tsx src/scripts/seed.ts"}, "dependencies": {"@waatch/shared": "workspace:*", "express": "catalog:", "cors": "catalog:", "helmet": "catalog:", "jsonwebtoken": "catalog:", "bcryptjs": "catalog:", "redis": "catalog:", "zod": "catalog:", "dotenv": "catalog:", "socket.io": "catalog:", "openai": "catalog:", "@prisma/client": "catalog:", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "morgan": "^1.10.0", "compression": "^1.7.4", "cookie-parser": "^1.4.6", "multer": "^1.4.5-lts.1", "uuid": "^9.0.1", "winston": "^3.11.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0"}, "devDependencies": {"@waatch/config": "workspace:*", "@types/express": "catalog:", "@types/cors": "catalog:", "@types/jsonwebtoken": "catalog:", "@types/bcryptjs": "catalog:", "@types/redis": "catalog:", "@types/socket.io": "catalog:", "@types/morgan": "^1.9.9", "@types/compression": "^1.7.5", "@types/cookie-parser": "^1.4.6", "@types/multer": "^1.4.11", "@types/uuid": "^9.0.7", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.6", "typescript": "catalog:", "tsx": "^4.6.2", "eslint": "catalog:", "vitest": "catalog:", "prisma": "catalog:", "@types/node": "^20.10.6"}, "engines": {"node": ">=20.10.0"}}