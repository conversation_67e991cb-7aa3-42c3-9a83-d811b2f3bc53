{"name": "@waatch/shared", "version": "1.0.0", "description": "Shared types, utilities, and constants for Waatch platform", "main": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}, "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts,.tsx"}, "dependencies": {"zod": "catalog:"}, "devDependencies": {"@waatch/config": "workspace:*", "typescript": "catalog:", "eslint": "catalog:"}, "publishConfig": {"access": "restricted"}}