// Prisma Schema for Waatch Platform
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Users and Authentication
model User {
  id          String   @id @default(cuid())
  email       String   @unique
  passwordHash String  @map("password_hash")
  firstName   String?  @map("first_name")
  lastName    String?  @map("last_name")
  avatarUrl   String?  @map("avatar_url")
  preferences Json     @default("{}")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")
  lastLogin   DateTime? @map("last_login")
  isActive    Boolean  @default(true) @map("is_active")

  // Relations
  chatSessions ChatSession[]
  projects     Project[]
  agentUsage   AgentUsage[]
  refreshTokens RefreshToken[]

  @@map("users")
}

// Refresh Tokens for JWT Authentication
model RefreshToken {
  id        String   @id @default(cuid())
  token     String   @unique
  userId    String   @map("user_id")
  expiresAt DateTime @map("expires_at")
  createdAt DateTime @default(now()) @map("created_at")
  isRevoked Boolean  @default(false) @map("is_revoked")

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("refresh_tokens")
}

// Agent Configurations
model AgentConfig {
  id          String   @id @default(cuid())
  agentId     String   @unique @map("agent_id")
  name        String
  description String?
  systemPrompt String  @map("system_prompt")
  capabilities Json
  tools       Json
  modelConfig Json     @map("model_config")
  version     String   @default("1.0.0")
  isActive    Boolean  @default(true) @map("is_active")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  @@map("agent_configs")
}

// Chat Sessions
model ChatSession {
  id             String   @id @default(cuid())
  userId         String   @map("user_id")
  agentId        String   @map("agent_id")
  title          String?
  context        Json     @default("{}")
  workspaceState Json     @default("{}") @map("workspace_state")
  createdAt      DateTime @default(now()) @map("created_at")
  updatedAt      DateTime @updatedAt @map("updated_at")
  lastActivity   DateTime @default(now()) @map("last_activity")
  isArchived     Boolean  @default(false) @map("is_archived")

  // Relations
  user     User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  messages Message[]
  projects Project[]

  @@index([userId])
  @@index([lastActivity])
  @@map("chat_sessions")
}

// Messages
model Message {
  id               String   @id @default(cuid())
  sessionId        String   @map("session_id")
  role             String   // 'user', 'assistant', 'system'
  content          String
  metadata         Json     @default("{}")
  toolCalls        Json?    @map("tool_calls")
  toolResults      Json?    @map("tool_results")
  tokensUsed       Int?     @map("tokens_used")
  processingTimeMs Int?     @map("processing_time_ms")
  createdAt        DateTime @default(now()) @map("created_at")

  // Relations
  session ChatSession @relation(fields: [sessionId], references: [id], onDelete: Cascade)

  @@index([sessionId])
  @@index([createdAt])
  @@map("messages")
}

// Projects
model Project {
  id          String   @id @default(cuid())
  userId      String   @map("user_id")
  sessionId   String?  @map("session_id")
  name        String
  description String?
  files       Json     @default("{}")
  framework   String?
  language    String?
  templateId  String?  @map("template_id")
  settings    Json     @default("{}")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // Relations
  user    User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  session ChatSession? @relation(fields: [sessionId], references: [id])

  @@index([userId])
  @@map("projects")
}

// Agent Usage Analytics
model AgentUsage {
  id              String   @id @default(cuid())
  userId          String   @map("user_id")
  agentId         String   @map("agent_id")
  sessionId       String?  @map("session_id")
  actionType      String   @map("action_type")
  toolUsed        String?  @map("tool_used")
  success         Boolean
  executionTimeMs Int?     @map("execution_time_ms")
  tokensConsumed  Int?     @map("tokens_consumed")
  errorMessage    String?  @map("error_message")
  createdAt       DateTime @default(now()) @map("created_at")

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId, agentId])
  @@index([createdAt])
  @@map("agent_usage")
}
